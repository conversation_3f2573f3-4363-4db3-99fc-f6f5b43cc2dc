<template>
  <view class="settings-page">
    <!-- 导航栏 -->
    <view class="nav-bar">
      <view class="nav-content">
        <view class="nav-left" @tap="goBack">
          <text class="nav-icon">←</text>
        </view>
        <text class="nav-title">设置</text>
        <view class="nav-right"></view>
      </view>
    </view>

    <!-- 设置内容 -->
    <view class="settings-content">
      <!-- 字幕设置 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-title">字幕设置</text>
        </view>
        
        <view class="setting-item" @tap="openFontSettings">
          <view class="item-content">
            <view class="item-icon">
              <text class="icon-text">🔤</text>
            </view>
            <view class="item-info">
              <text class="item-title">字体大小</text>
              <text class="item-desc">调整字幕字体大小和显示效果</text>
            </view>
          </view>
          <text class="item-arrow">›</text>
        </view>
      </view>

      <!-- 视频设置 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-title">视频设置</text>
        </view>
        
        <view class="setting-item">
          <view class="item-content">
            <view class="item-icon">
              <text class="icon-text">📹</text>
            </view>
            <view class="item-info">
              <text class="item-title">视频质量</text>
              <text class="item-desc">选择视频处理质量</text>
            </view>
          </view>
          <text class="item-value">高质量</text>
        </view>
        
        <view class="setting-item">
          <view class="item-content">
            <view class="item-icon">
              <text class="icon-text">⏱️</text>
            </view>
            <view class="item-info">
              <text class="item-title">处理超时</text>
              <text class="item-desc">视频处理最大等待时间</text>
            </view>
          </view>
          <text class="item-value">10分钟</text>
        </view>
      </view>

      <!-- 语言设置 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-title">语言设置</text>
        </view>
        
        <view class="setting-item">
          <view class="item-content">
            <view class="item-icon">
              <text class="icon-text">🌍</text>
            </view>
            <view class="item-info">
              <text class="item-title">默认源语言</text>
              <text class="item-desc">视频语言识别的默认选项</text>
            </view>
          </view>
          <text class="item-value">自动识别</text>
        </view>
        
        <view class="setting-item">
          <view class="item-content">
            <view class="item-icon">
              <text class="icon-text">🔄</text>
            </view>
            <view class="item-info">
              <text class="item-title">默认目标语言</text>
              <text class="item-desc">翻译的默认目标语言</text>
            </view>
          </view>
          <text class="item-value">中文</text>
        </view>
      </view>

      <!-- 其他设置 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-title">其他</text>
        </view>
        
        <view class="setting-item">
          <view class="item-content">
            <view class="item-icon">
              <text class="icon-text">🔔</text>
            </view>
            <view class="item-info">
              <text class="item-title">消息通知</text>
              <text class="item-desc">处理完成时的通知提醒</text>
            </view>
          </view>
          <switch :checked="true" color="#4f46e5" />
        </view>
        
        <view class="setting-item">
          <view class="item-content">
            <view class="item-icon">
              <text class="icon-text">🗑️</text>
            </view>
            <view class="item-info">
              <text class="item-title">自动清理</text>
              <text class="item-desc">自动清理过期的处理记录</text>
            </view>
          </view>
          <switch :checked="false" color="#4f46e5" />
        </view>
      </view>

      <!-- 关于 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-title">关于</text>
        </view>
        
        <view class="setting-item">
          <view class="item-content">
            <view class="item-icon">
              <text class="icon-text">ℹ️</text>
            </view>
            <view class="item-info">
              <text class="item-title">版本信息</text>
              <text class="item-desc">当前应用版本</text>
            </view>
          </view>
          <text class="item-value">v1.0.0</text>
        </view>
        
        <view class="setting-item">
          <view class="item-content">
            <view class="item-icon">
              <text class="icon-text">📞</text>
            </view>
            <view class="item-info">
              <text class="item-title">联系我们</text>
              <text class="item-desc">意见反馈和技术支持</text>
            </view>
          </view>
          <text class="item-arrow">›</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 打开字体设置
const openFontSettings = () => {
  uni.navigateTo({
    url: '/pages/font-settings/font-settings'
  })
}
</script>

<style scoped>
.settings-page {
  min-height: 100vh;
  background-color: #fafafa;
}

.nav-bar {
  background: white;
  border-bottom: 1rpx solid #e5e7eb;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
}

.nav-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-icon {
  font-size: 36rpx;
  color: #4f46e5;
  font-weight: 600;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.nav-right {
  width: 60rpx;
}

.settings-content {
  padding: 32rpx;
}

.settings-section {
  margin-bottom: 48rpx;
}

.section-header {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 1rpx;
}

.setting-item {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.setting-item:active {
  transform: scale(0.98);
  background-color: #f9fafb;
}

.item-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.item-icon {
  width: 72rpx;
  height: 72rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.icon-text {
  font-size: 32rpx;
}

.item-info {
  flex: 1;
}

.item-title {
  display: block;
  font-size: 30rpx;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.item-desc {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.4;
}

.item-value {
  font-size: 26rpx;
  color: #4f46e5;
  font-weight: 500;
  margin-right: 16rpx;
}

.item-arrow {
  font-size: 32rpx;
  color: #9ca3af;
  font-weight: 300;
}
</style>
