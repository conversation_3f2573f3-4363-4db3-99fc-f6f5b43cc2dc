/**
 * 云端字幕字体大小计算器
 * 与前端字体大小管理器保持一致的计算逻辑
 */

// 字体大小级别枚举
const FontSizeLevel = {
  EXTRA_SMALL: 'xs',    // 极小 70%
  SMALL: 'sm',          // 小 85%
  MEDIUM: 'md',         // 标准 100%
  LARGE: 'lg',          // 大 115%
  EXTRA_LARGE: 'xl'     // 极大 130%
};

// 视频分辨率级别
const ResolutionLevel = {
  SD_480P: '480p',      // 标清
  HD_720P: '720p',      // 高清
  FHD_1080P: '1080p',   // 全高清
  QHD_1440P: '1440p',   // 2K
  UHD_4K: '4k',         // 4K
  UHD_8K: '8k'          // 8K
};

// 设备类型
const DeviceType = {
  MOBILE_PORTRAIT: 'mobile_portrait',   // 手机竖屏
  MOBILE_LANDSCAPE: 'mobile_landscape', // 手机横屏
  TABLET: 'tablet',                     // 平板
  DESKTOP: 'desktop'                    // 桌面
};

// 视频方向类型
const VideoOrientation = {
  HORIZONTAL: 'horizontal',  // 横屏视频
  VERTICAL: 'vertical',      // 竖屏视频
  SQUARE: 'square'           // 正方形视频
};

// 默认字体大小缩放配置
const DEFAULT_FONT_SIZE_SCALES = {
  [FontSizeLevel.EXTRA_SMALL]: 0.7,
  [FontSizeLevel.SMALL]: 0.85,
  [FontSizeLevel.MEDIUM]: 1.0,
  [FontSizeLevel.LARGE]: 1.15,
  [FontSizeLevel.EXTRA_LARGE]: 1.3
};

// 默认分辨率配置
const DEFAULT_RESOLUTION_CONFIGS = {
  [ResolutionLevel.SD_480P]: {
    baseSize: 18,
    minSize: 12,
    maxSize: 24,
    marginBase: 20
  },
  [ResolutionLevel.HD_720P]: {
    baseSize: 24,
    minSize: 16,
    maxSize: 32,
    marginBase: 30
  },
  [ResolutionLevel.FHD_1080P]: {
    baseSize: 32,
    minSize: 22,
    maxSize: 42,
    marginBase: 40
  },
  [ResolutionLevel.QHD_1440P]: {
    baseSize: 44,
    minSize: 30,
    maxSize: 58,
    marginBase: 50
  },
  [ResolutionLevel.UHD_4K]: {
    baseSize: 64,
    minSize: 44,
    maxSize: 84,
    marginBase: 60
  },
  [ResolutionLevel.UHD_8K]: {
    baseSize: 120,
    minSize: 84,
    maxSize: 156,
    marginBase: 80
  }
};

// 默认设备调整配置
const DEFAULT_DEVICE_ADJUSTMENTS = {
  [DeviceType.MOBILE_PORTRAIT]: {
    fontScale: 0.65,
    marginScale: 1.2,
    sideMargin: 16
  },
  [DeviceType.MOBILE_LANDSCAPE]: {
    fontScale: 0.8,
    marginScale: 1.0,
    sideMargin: 20
  },
  [DeviceType.TABLET]: {
    fontScale: 0.9,
    marginScale: 1.0,
    sideMargin: 24
  },
  [DeviceType.DESKTOP]: {
    fontScale: 1.0,
    marginScale: 1.0,
    sideMargin: 32
  }
};

// 默认视频方向调整配置
const DEFAULT_ORIENTATION_ADJUSTMENTS = {
  [VideoOrientation.HORIZONTAL]: {
    fontScale: 1.0,
    marginScale: 1.0
  },
  [VideoOrientation.VERTICAL]: {
    fontScale: 0.75,
    marginScale: 1.3
  },
  [VideoOrientation.SQUARE]: {
    fontScale: 0.9,
    marginScale: 1.1
  }
};

// 默认语言调整配置
const DEFAULT_LANGUAGE_ADJUSTMENTS = {
  'zh': {
    marginExtra: 8,
    verticalExtra: 12,
    fontScale: 1.0
  },
  'zh-cn': {
    marginExtra: 8,
    verticalExtra: 12,
    fontScale: 1.0
  },
  'zh-tw': {
    marginExtra: 8,
    verticalExtra: 12,
    fontScale: 1.0
  },
  'ja': {
    marginExtra: 6,
    verticalExtra: 10,
    fontScale: 0.95
  },
  'ko': {
    marginExtra: 4,
    verticalExtra: 8,
    fontScale: 0.95
  },
  'en': {
    marginExtra: 4,
    verticalExtra: 6,
    fontScale: 1.0
  },
  'default': {
    marginExtra: 4,
    verticalExtra: 6,
    fontScale: 1.0
  }
};

/**
 * 检测分辨率级别
 */
function detectResolutionLevel(videoWidth, videoHeight) {
  const minDimension = Math.min(videoWidth, videoHeight);
  
  if (minDimension >= 4320) return ResolutionLevel.UHD_8K;
  if (minDimension >= 2160) return ResolutionLevel.UHD_4K;
  if (minDimension >= 1440) return ResolutionLevel.QHD_1440P;
  if (minDimension >= 1080) return ResolutionLevel.FHD_1080P;
  if (minDimension >= 720) return ResolutionLevel.HD_720P;
  return ResolutionLevel.SD_480P;
}

/**
 * 检测视频方向
 */
function detectVideoOrientation(videoWidth, videoHeight) {
  const aspectRatio = videoWidth / videoHeight;
  
  if (aspectRatio > 1.1) return VideoOrientation.HORIZONTAL;
  if (aspectRatio < 0.9) return VideoOrientation.VERTICAL;
  return VideoOrientation.SQUARE;
}

/**
 * 计算字体大小
 * @param {number} videoWidth - 视频宽度
 * @param {number} videoHeight - 视频高度
 * @param {string} targetLanguage - 目标语言
 * @param {string} userFontLevel - 用户字体大小级别
 * @param {string} deviceType - 设备类型（可选）
 * @returns {Object} 字体大小计算结果
 */
function calculateFontSize(
  videoWidth, 
  videoHeight, 
  targetLanguage = 'default', 
  userFontLevel = FontSizeLevel.MEDIUM,
  deviceType = DeviceType.DESKTOP
) {
  // 检测各种参数
  const resolutionLevel = detectResolutionLevel(videoWidth, videoHeight);
  const videoOrientation = detectVideoOrientation(videoWidth, videoHeight);

  // 获取配置
  const resConfig = DEFAULT_RESOLUTION_CONFIGS[resolutionLevel] || DEFAULT_RESOLUTION_CONFIGS[ResolutionLevel.FHD_1080P];
  const deviceConfig = DEFAULT_DEVICE_ADJUSTMENTS[deviceType] || DEFAULT_DEVICE_ADJUSTMENTS[DeviceType.DESKTOP];
  const orientationConfig = DEFAULT_ORIENTATION_ADJUSTMENTS[videoOrientation] || DEFAULT_ORIENTATION_ADJUSTMENTS[VideoOrientation.HORIZONTAL];
  const languageConfig = DEFAULT_LANGUAGE_ADJUSTMENTS[targetLanguage] || DEFAULT_LANGUAGE_ADJUSTMENTS['default'];

  // 获取用户选择的缩放系数
  const userScale = DEFAULT_FONT_SIZE_SCALES[userFontLevel] || 1.0;

  // 计算各种缩放系数
  const deviceScale = deviceConfig.fontScale;
  const orientationScale = orientationConfig.fontScale;
  const languageScale = languageConfig.fontScale || 1.0;

  // 计算最终字体大小
  const finalFontSize = Math.round(
    resConfig.baseSize * userScale * deviceScale * orientationScale * languageScale
  );

  // 确保字体大小在合理范围内
  const clampedFontSize = Math.max(
    resConfig.minSize,
    Math.min(resConfig.maxSize, finalFontSize)
  );

  // 计算边距
  const baseMarginV = resConfig.marginBase * deviceConfig.marginScale * orientationConfig.marginScale;
  const languageMarginExtra = videoOrientation === VideoOrientation.VERTICAL 
    ? languageConfig.verticalExtra 
    : languageConfig.marginExtra;
  
  const finalMarginV = Math.round(baseMarginV + languageMarginExtra);
  const sideMargin = deviceConfig.sideMargin;

  return {
    fontSize: clampedFontSize,
    marginV: finalMarginV,
    marginL: sideMargin,
    marginR: sideMargin,
    resolutionLevel,
    deviceType,
    videoOrientation,
    appliedScales: {
      userScale,
      deviceScale,
      orientationScale,
      languageScale
    }
  };
}

module.exports = {
  FontSizeLevel,
  ResolutionLevel,
  DeviceType,
  VideoOrientation,
  calculateFontSize,
  detectResolutionLevel,
  detectVideoOrientation
};
