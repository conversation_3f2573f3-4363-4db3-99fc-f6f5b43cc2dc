/**
 * 字幕字体大小管理器
 * 负责字体大小的计算、存储和应用
 */

import {
  SubtitleFontConfig,
  FontSizeLevel,
  ResolutionLevel,
  DeviceType,
  VideoOrientation,
  DEFAULT_SUBTITLE_FONT_CONFIG,
  DEFAULT_RESOLUTION_CONFIGS,
  DEFAULT_DEVICE_ADJUSTMENTS,
  DEFAULT_ORIENTATION_ADJUSTMENTS,
  DEFAULT_LANGUAGE_ADJUSTMENTS
} from '@/config/subtitle-font'

// 存储键名
const FONT_CONFIG_STORAGE_KEY = 'subtitle_font_config'

// 计算结果接口
export interface FontSizeCalculationResult {
  fontSize: number;           // 最终字体大小(px)
  marginV: number;           // 垂直边距(px)
  marginL: number;           // 左边距(px)
  marginR: number;           // 右边距(px)
  resolutionLevel: ResolutionLevel;  // 匹配的分辨率级别
  deviceType: DeviceType;    // 检测的设备类型
  videoOrientation: VideoOrientation; // 视频方向
  appliedScales: {           // 应用的缩放系数
    userScale: number;       // 用户选择的缩放
    deviceScale: number;     // 设备缩放
    orientationScale: number; // 方向缩放
    languageScale: number;   // 语言缩放
  };
}

class SubtitleFontManager {
  private config: SubtitleFontConfig;

  constructor() {
    this.config = this.loadConfig();
  }

  /**
   * 从本地存储加载配置
   */
  private loadConfig(): SubtitleFontConfig {
    try {
      const savedConfig = uni.getStorageSync(FONT_CONFIG_STORAGE_KEY);
      if (savedConfig && typeof savedConfig === 'object') {
        // 合并保存的配置和默认配置，确保新增的配置项有默认值
        return {
          ...DEFAULT_SUBTITLE_FONT_CONFIG,
          ...savedConfig,
          fontSizeScales: {
            ...DEFAULT_SUBTITLE_FONT_CONFIG.fontSizeScales,
            ...(savedConfig.fontSizeScales || {})
          },
          resolutionConfigs: {
            ...DEFAULT_SUBTITLE_FONT_CONFIG.resolutionConfigs,
            ...(savedConfig.resolutionConfigs || {})
          },
          deviceAdjustments: {
            ...DEFAULT_SUBTITLE_FONT_CONFIG.deviceAdjustments,
            ...(savedConfig.deviceAdjustments || {})
          },
          orientationAdjustments: {
            ...DEFAULT_SUBTITLE_FONT_CONFIG.orientationAdjustments,
            ...(savedConfig.orientationAdjustments || {})
          },
          languageAdjustments: {
            ...DEFAULT_SUBTITLE_FONT_CONFIG.languageAdjustments,
            ...(savedConfig.languageAdjustments || {})
          }
        };
      }
    } catch (error) {
      console.warn('加载字体配置失败，使用默认配置:', error);
    }
    return { ...DEFAULT_SUBTITLE_FONT_CONFIG };
  }

  /**
   * 保存配置到本地存储
   */
  private saveConfig(): void {
    try {
      this.config.lastUpdated = Date.now();
      uni.setStorageSync(FONT_CONFIG_STORAGE_KEY, this.config);
    } catch (error) {
      console.error('保存字体配置失败:', error);
      throw new Error('保存字体配置失败');
    }
  }

  /**
   * 获取当前配置
   */
  getConfig(): SubtitleFontConfig {
    return { ...this.config };
  }

  /**
   * 设置用户字体大小级别
   */
  setUserFontLevel(level: FontSizeLevel): void {
    this.config.userFontLevel = level;
    this.saveConfig();
  }

  /**
   * 获取用户字体大小级别
   */
  getUserFontLevel(): FontSizeLevel {
    return this.config.userFontLevel;
  }

  /**
   * 检测分辨率级别
   */
  private detectResolutionLevel(videoWidth: number, videoHeight: number): ResolutionLevel {
    const minDimension = Math.min(videoWidth, videoHeight);
    
    if (minDimension >= 4320) return ResolutionLevel.UHD_8K;
    if (minDimension >= 2160) return ResolutionLevel.UHD_4K;
    if (minDimension >= 1440) return ResolutionLevel.QHD_1440P;
    if (minDimension >= 1080) return ResolutionLevel.FHD_1080P;
    if (minDimension >= 720) return ResolutionLevel.HD_720P;
    return ResolutionLevel.SD_480P;
  }

  /**
   * 检测设备类型
   */
  private detectDeviceType(): DeviceType {
    try {
      const systemInfo = uni.getSystemInfoSync();
      const { platform, screenWidth, screenHeight } = systemInfo;
      
      // 判断是否为移动设备
      const isMobile = platform === 'ios' || platform === 'android';
      
      if (isMobile) {
        // 判断屏幕方向
        const isPortrait = screenHeight > screenWidth;
        return isPortrait ? DeviceType.MOBILE_PORTRAIT : DeviceType.MOBILE_LANDSCAPE;
      }
      
      // 判断是否为平板（基于屏幕尺寸）
      const screenSize = Math.max(screenWidth, screenHeight);
      if (screenSize >= 768 && screenSize < 1024) {
        return DeviceType.TABLET;
      }
      
      return DeviceType.DESKTOP;
    } catch (error) {
      console.warn('检测设备类型失败，使用默认值:', error);
      return DeviceType.MOBILE_PORTRAIT;
    }
  }

  /**
   * 检测视频方向
   */
  private detectVideoOrientation(videoWidth: number, videoHeight: number): VideoOrientation {
    const aspectRatio = videoWidth / videoHeight;
    
    if (aspectRatio > 1.1) return VideoOrientation.HORIZONTAL;
    if (aspectRatio < 0.9) return VideoOrientation.VERTICAL;
    return VideoOrientation.SQUARE;
  }

  /**
   * 计算字体大小
   */
  calculateFontSize(
    videoWidth: number,
    videoHeight: number,
    targetLanguage: string = 'default'
  ): FontSizeCalculationResult {
    // 检测各种参数
    const resolutionLevel = this.detectResolutionLevel(videoWidth, videoHeight);
    const deviceType = this.detectDeviceType();
    const videoOrientation = this.detectVideoOrientation(videoWidth, videoHeight);

    // 获取配置
    const resConfig = this.config.resolutionConfigs[resolutionLevel] || DEFAULT_RESOLUTION_CONFIGS[ResolutionLevel.FHD_1080P];
    const deviceConfig = this.config.deviceAdjustments[deviceType] || DEFAULT_DEVICE_ADJUSTMENTS[DeviceType.MOBILE_PORTRAIT];
    const orientationConfig = this.config.orientationAdjustments[videoOrientation] || DEFAULT_ORIENTATION_ADJUSTMENTS[VideoOrientation.HORIZONTAL];
    const languageConfig = this.config.languageAdjustments[targetLanguage] || this.config.languageAdjustments['default'] || DEFAULT_LANGUAGE_ADJUSTMENTS['default'];

    // 获取用户选择的缩放系数
    const userScale = this.config.fontSizeScales[this.config.userFontLevel];

    // 计算各种缩放系数
    const deviceScale = deviceConfig.fontScale;
    const orientationScale = orientationConfig.fontScale;
    const languageScale = languageConfig.fontScale || 1.0;

    // 计算最终字体大小
    const finalFontSize = Math.round(
      resConfig.baseSize * userScale * deviceScale * orientationScale * languageScale
    );

    // 确保字体大小在合理范围内
    const clampedFontSize = Math.max(
      resConfig.minSize,
      Math.min(resConfig.maxSize, finalFontSize)
    );

    // 计算边距
    const baseMarginV = resConfig.marginBase * deviceConfig.marginScale * orientationConfig.marginScale;
    const languageMarginExtra = videoOrientation === VideoOrientation.VERTICAL 
      ? languageConfig.verticalExtra 
      : languageConfig.marginExtra;
    
    const finalMarginV = Math.round(baseMarginV + languageMarginExtra);
    const sideMargin = deviceConfig.sideMargin;

    return {
      fontSize: clampedFontSize,
      marginV: finalMarginV,
      marginL: sideMargin,
      marginR: sideMargin,
      resolutionLevel,
      deviceType,
      videoOrientation,
      appliedScales: {
        userScale,
        deviceScale,
        orientationScale,
        languageScale
      }
    };
  }

  /**
   * 重置为默认配置
   */
  resetToDefault(): void {
    this.config = { ...DEFAULT_SUBTITLE_FONT_CONFIG };
    this.saveConfig();
  }

  /**
   * 更新配置的某个部分
   */
  updateConfig(updates: Partial<SubtitleFontConfig>): void {
    this.config = {
      ...this.config,
      ...updates
    };
    this.saveConfig();
  }

  /**
   * 获取字体大小预览信息
   */
  getPreviewInfo(level: FontSizeLevel): { scale: number; description: string } {
    const scale = this.config.fontSizeScales[level];
    const percentage = Math.round(scale * 100);
    
    return {
      scale,
      description: `${percentage}% - 相对于标准大小`
    };
  }
}

// 导出单例实例
export const subtitleFontManager = new SubtitleFontManager();

// 导出类型和工具函数
export { SubtitleFontManager };
export type { FontSizeCalculationResult };
