<template>
  <view class="font-settings">
    <!-- 设置标题 -->
    <view class="settings-header">
      <text class="header-title">字幕字体大小</text>
      <text class="header-subtitle">调整字幕显示效果，让观看更舒适</text>
    </view>

    <!-- 字体大小选择器 -->
    <view class="font-size-selector">
      <view class="selector-title">
        <text class="title-text">字体大小</text>
        <text class="current-level">{{ currentLevelName }}</text>
      </view>
      
      <view class="size-options">
        <view 
          v-for="level in fontSizeLevels" 
          :key="level.value"
          class="size-option"
          :class="{ 'active': currentFontLevel === level.value }"
          @tap="selectFontLevel(level.value)"
        >
          <view class="option-content">
            <text class="option-name">{{ level.name }}</text>
            <text class="option-scale">{{ level.scale }}%</text>
          </view>
          <text class="option-description">{{ level.description }}</text>
        </view>
      </view>
    </view>

    <!-- 预览区域 -->
    <view class="preview-section">
      <view class="preview-title">
        <text class="title-text">预览效果</text>
        <text class="preview-hint">实际效果会根据视频分辨率自动调整</text>
      </view>
      
      <view class="preview-container">
        <view class="preview-video">
          <view class="video-placeholder">
            <text class="video-text">视频内容</text>
          </view>
          <view class="preview-subtitle" :style="previewSubtitleStyle">
            <text class="subtitle-text">这是字幕预览文本</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 自适应开关 -->
    <view class="adaptive-setting">
      <view class="setting-item">
        <view class="setting-info">
          <text class="setting-name">智能自适应</text>
          <text class="setting-desc">根据设备和视频类型自动优化字体大小</text>
        </view>
        <switch 
          :checked="enableAdaptive" 
          @change="onAdaptiveChange"
          color="#4f46e5"
        />
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="btn btn-secondary" @tap="resetToDefault">
        恢复默认
      </button>
      <button class="btn btn-primary" @tap="saveSettings">
        保存设置
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { 
  FontSizeLevel, 
  FONT_SIZE_LEVEL_NAMES, 
  FONT_SIZE_LEVEL_DESCRIPTIONS,
  DEFAULT_FONT_SIZE_SCALES
} from '@/config/subtitle-font'
import { subtitleFontManager } from '@/utils/subtitle-font-manager'
import { updateSubtitleFontConfig, getSubtitleFontConfig } from '@/config/index'

// 响应式数据
const currentFontLevel = ref<FontSizeLevel>(FontSizeLevel.MEDIUM)
const enableAdaptive = ref(true)

// 字体大小级别选项
const fontSizeLevels = computed(() => [
  {
    value: FontSizeLevel.EXTRA_SMALL,
    name: FONT_SIZE_LEVEL_NAMES[FontSizeLevel.EXTRA_SMALL],
    scale: Math.round(DEFAULT_FONT_SIZE_SCALES[FontSizeLevel.EXTRA_SMALL] * 100),
    description: FONT_SIZE_LEVEL_DESCRIPTIONS[FontSizeLevel.EXTRA_SMALL]
  },
  {
    value: FontSizeLevel.SMALL,
    name: FONT_SIZE_LEVEL_NAMES[FontSizeLevel.SMALL],
    scale: Math.round(DEFAULT_FONT_SIZE_SCALES[FontSizeLevel.SMALL] * 100),
    description: FONT_SIZE_LEVEL_DESCRIPTIONS[FontSizeLevel.SMALL]
  },
  {
    value: FontSizeLevel.MEDIUM,
    name: FONT_SIZE_LEVEL_NAMES[FontSizeLevel.MEDIUM],
    scale: Math.round(DEFAULT_FONT_SIZE_SCALES[FontSizeLevel.MEDIUM] * 100),
    description: FONT_SIZE_LEVEL_DESCRIPTIONS[FontSizeLevel.MEDIUM]
  },
  {
    value: FontSizeLevel.LARGE,
    name: FONT_SIZE_LEVEL_NAMES[FontSizeLevel.LARGE],
    scale: Math.round(DEFAULT_FONT_SIZE_SCALES[FontSizeLevel.LARGE] * 100),
    description: FONT_SIZE_LEVEL_DESCRIPTIONS[FontSizeLevel.LARGE]
  },
  {
    value: FontSizeLevel.EXTRA_LARGE,
    name: FONT_SIZE_LEVEL_NAMES[FontSizeLevel.EXTRA_LARGE],
    scale: Math.round(DEFAULT_FONT_SIZE_SCALES[FontSizeLevel.EXTRA_LARGE] * 100),
    description: FONT_SIZE_LEVEL_DESCRIPTIONS[FontSizeLevel.EXTRA_LARGE]
  }
])

// 当前级别名称
const currentLevelName = computed(() => {
  const level = fontSizeLevels.value.find(l => l.value === currentFontLevel.value)
  return level ? level.name : '标准'
})

// 预览字幕样式
const previewSubtitleStyle = computed(() => {
  const scale = DEFAULT_FONT_SIZE_SCALES[currentFontLevel.value] || 1.0
  const baseFontSize = 32 // 基础字体大小 (rpx)
  const fontSize = Math.round(baseFontSize * scale)
  
  return {
    fontSize: `${fontSize}rpx`,
    transition: 'all 0.3s ease'
  }
})

// 选择字体级别
const selectFontLevel = (level: FontSizeLevel) => {
  currentFontLevel.value = level
  subtitleFontManager.setUserFontLevel(level)
}

// 自适应开关变化
const onAdaptiveChange = (event: any) => {
  enableAdaptive.value = event.detail.value
  subtitleFontManager.updateConfig({
    enableAdaptive: enableAdaptive.value
  })
}

// 恢复默认设置
const resetToDefault = () => {
  uni.showModal({
    title: '确认重置',
    content: '确定要恢复默认字体设置吗？',
    success: (res) => {
      if (res.confirm) {
        subtitleFontManager.resetToDefault()
        loadCurrentSettings()
        uni.showToast({
          title: '已恢复默认设置',
          icon: 'success'
        })
      }
    }
  })
}

// 保存设置
const saveSettings = () => {
  try {
    // 更新应用配置
    updateSubtitleFontConfig({
      userFontLevel: currentFontLevel.value,
      enableAdaptive: enableAdaptive.value
    })
    
    uni.showToast({
      title: '设置已保存',
      icon: 'success'
    })
  } catch (error) {
    uni.showToast({
      title: '保存失败',
      icon: 'error'
    })
  }
}

// 加载当前设置
const loadCurrentSettings = () => {
  try {
    const config = getSubtitleFontConfig()
    currentFontLevel.value = config.userFontLevel as FontSizeLevel
    enableAdaptive.value = config.enableAdaptive
  } catch (error) {
    console.warn('加载字体设置失败:', error)
  }
}

// 组件挂载时加载设置
onMounted(() => {
  loadCurrentSettings()
})
</script>

<style scoped>
.font-settings {
  padding: 32rpx;
  background-color: #fafafa;
  min-height: 100vh;
}

.settings-header {
  margin-bottom: 48rpx;
  text-align: center;
}

.header-title {
  display: block;
  font-size: 44rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12rpx;
}

.header-subtitle {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
}

.font-size-selector {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.06);
}

.selector-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 2rpx solid #f3f4f6;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.current-level {
  font-size: 28rpx;
  color: #4f46e5;
  font-weight: 500;
}

.size-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.size-option {
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.size-option.active {
  border-color: #4f46e5;
  background-color: #f8faff;
}

.option-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.option-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #1f2937;
}

.option-scale {
  font-size: 26rpx;
  color: #4f46e5;
  font-weight: 600;
}

.option-description {
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.4;
}

.preview-section {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.06);
}

.preview-title {
  margin-bottom: 24rpx;
}

.preview-hint {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  margin-top: 8rpx;
}

.preview-container {
  position: relative;
  border-radius: 16rpx;
  overflow: hidden;
}

.preview-video {
  position: relative;
  width: 100%;
  height: 300rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-text {
  color: white;
  font-size: 28rpx;
  opacity: 0.8;
}

.preview-subtitle {
  position: absolute;
  bottom: 32rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
}

.subtitle-text {
  color: #ffff00;
  font-weight: 500;
  text-align: center;
}

.adaptive-setting {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 48rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.06);
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.setting-info {
  flex: 1;
  margin-right: 32rpx;
}

.setting-name {
  display: block;
  font-size: 30rpx;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.setting-desc {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.4;
}

.action-buttons {
  display: flex;
  gap: 24rpx;
}

.btn {
  flex: 1;
  height: 88rpx;
  border-radius: 16rpx;
  font-size: 30rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.btn-secondary {
  background-color: #f3f4f6;
  color: #374151;
}

.btn-secondary:active {
  background-color: #e5e7eb;
}

.btn-primary {
  background-color: #4f46e5;
  color: white;
}

.btn-primary:active {
  background-color: #4338ca;
}
</style>
