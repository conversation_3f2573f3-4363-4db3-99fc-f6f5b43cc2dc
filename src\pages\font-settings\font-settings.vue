<template>
  <view class="font-settings-page">
    <!-- 导航栏 -->
    <view class="nav-bar">
      <view class="nav-content">
        <view class="nav-left" @tap="goBack">
          <text class="nav-icon">←</text>
        </view>
        <text class="nav-title">字体设置</text>
        <view class="nav-right"></view>
      </view>
    </view>

    <!-- 字体设置组件 -->
    <SubtitleFontSettings />
  </view>
</template>

<script setup lang="ts">
import SubtitleFontSettings from '@/components/SubtitleFontSettings.vue'

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}
</script>

<style scoped>
.font-settings-page {
  min-height: 100vh;
  background-color: #fafafa;
}

.nav-bar {
  background: white;
  border-bottom: 1rpx solid #e5e7eb;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
}

.nav-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-icon {
  font-size: 36rpx;
  color: #4f46e5;
  font-weight: 600;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.nav-right {
  width: 60rpx;
}
</style>
