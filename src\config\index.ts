/**
 * 配置管理入口文件
 * 提供统一的配置管理接口
 */

import { defaultConfig, type AppConfig, type VideoUploadConfig, type SubtitleFontConfig } from './app'

// 配置存储键名
const CONFIG_STORAGE_KEY = 'app_config'

// 当前配置（运行时可修改）
let currentConfig: AppConfig = { ...defaultConfig }

/**
 * 初始化配置
 * 从本地存储加载配置，如果不存在则使用默认配置
 */
export const initConfig = (): void => {
  try {
    const savedConfig = uni.getStorageSync(CONFIG_STORAGE_KEY)
    if (savedConfig && typeof savedConfig === 'object') {
      // 合并保存的配置和默认配置，确保新增的配置项有默认值
      currentConfig = {
        ...defaultConfig,
        ...savedConfig,
        videoUpload: {
          ...defaultConfig.videoUpload,
          ...(savedConfig.videoUpload || {})
        },
        subtitleFont: {
          ...defaultConfig.subtitleFont,
          ...(savedConfig.subtitleFont || {})
        }
      }
    }
  } catch (error) {
    currentConfig = { ...defaultConfig }
  }
}

/**
 * 保存配置到本地存储
 */
export const saveConfig = (config: AppConfig): void => {
  try {
    currentConfig = { ...config }
    uni.setStorageSync(CONFIG_STORAGE_KEY, currentConfig)
  } catch (error) {
    throw new Error('保存配置失败')
  }
}

/**
 * 获取当前配置
 */
export const getCurrentConfig = (): AppConfig => {
  return { ...currentConfig }
}

/**
 * 获取视频上传配置
 */
export const getVideoUploadConfig = (): VideoUploadConfig => {
  return { ...currentConfig.videoUpload }
}

/**
 * 更新视频上传配置
 */
export const updateVideoUploadConfig = (config: Partial<VideoUploadConfig>): void => {
  const newConfig: AppConfig = {
    ...currentConfig,
    videoUpload: {
      ...currentConfig.videoUpload,
      ...config
    }
  }
  
  // 如果修改了文件大小，同步更新MB显示值
  if (config.maxFileSize !== undefined) {
    newConfig.videoUpload.maxFileSizeMB = Math.round(config.maxFileSize / (1024 * 1024))
  }
  
  saveConfig(newConfig)
}

/**
 * 获取字幕字体配置
 */
export const getSubtitleFontConfig = (): SubtitleFontConfig => {
  return { ...currentConfig.subtitleFont }
}

/**
 * 更新字幕字体配置
 */
export const updateSubtitleFontConfig = (config: Partial<SubtitleFontConfig>): void => {
  const newConfig: AppConfig = {
    ...currentConfig,
    subtitleFont: {
      ...currentConfig.subtitleFont,
      ...config
    }
  }

  saveConfig(newConfig)
}

/**
 * 重置为默认配置
 */
export const resetToDefaultConfig = (): void => {
  saveConfig({ ...defaultConfig })
}

/**
 * 验证配置的有效性
 */
export const validateConfig = (config: AppConfig): { valid: boolean; errors: string[] } => {
  const errors: string[] = []
  
  // 验证视频上传配置
  const { videoUpload } = config
  
  if (videoUpload.maxFileSize <= 0) {
    errors.push('文件大小限制必须大于0')
  }
  
  if (videoUpload.maxFileSize > 500 * 1024 * 1024) {
    errors.push('文件大小限制不能超过500MB')
  }
  
  if (videoUpload.maxDuration <= 0) {
    errors.push('视频时长限制必须大于0')
  }
  
  if (videoUpload.maxDuration > 1800) {
    errors.push('视频时长限制不能超过30分钟')
  }
  
  if (!videoUpload.supportedFormats || videoUpload.supportedFormats.length === 0) {
    errors.push('必须指定至少一种支持的视频格式')
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

// 应用启动时自动初始化配置
initConfig()

// 导出配置相关的所有函数
export * from './app'
