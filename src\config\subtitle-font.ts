/**
 * 字幕字体大小配置系统
 * 基于现代化设计原则和主流视频平台标准
 */

// 字体大小级别枚举
export enum FontSizeLevel {
  EXTRA_SMALL = 'xs',    // 极小 70%
  SMALL = 'sm',          // 小 85%
  MEDIUM = 'md',         // 标准 100%
  LARGE = 'lg',          // 大 115%
  EXTRA_LARGE = 'xl'     // 极大 130%
}

// 视频分辨率级别
export enum ResolutionLevel {
  SD_480P = '480p',      // 标清
  HD_720P = '720p',      // 高清
  FHD_1080P = '1080p',   // 全高清
  QHD_1440P = '1440p',   // 2K
  UHD_4K = '4k',         // 4K
  UHD_8K = '8k'          // 8K
}

// 设备类型
export enum DeviceType {
  MOBILE_PORTRAIT = 'mobile_portrait',   // 手机竖屏
  MOBILE_LANDSCAPE = 'mobile_landscape', // 手机横屏
  TABLET = 'tablet',                     // 平板
  DESKTOP = 'desktop'                    // 桌面
}

// 视频方向类型
export enum VideoOrientation {
  HORIZONTAL = 'horizontal',  // 横屏视频
  VERTICAL = 'vertical',      // 竖屏视频
  SQUARE = 'square'           // 正方形视频
}

// 字体大小缩放配置
export interface FontSizeScale {
  [FontSizeLevel.EXTRA_SMALL]: number;  // 0.7
  [FontSizeLevel.SMALL]: number;        // 0.85
  [FontSizeLevel.MEDIUM]: number;       // 1.0
  [FontSizeLevel.LARGE]: number;        // 1.15
  [FontSizeLevel.EXTRA_LARGE]: number;  // 1.3
}

// 分辨率基础字体大小配置
export interface ResolutionFontConfig {
  baseSize: number;        // 基础字体大小(px)
  minSize: number;         // 最小字体大小(px)
  maxSize: number;         // 最大字体大小(px)
  marginBase: number;      // 基础边距(px)
}

// 设备类型调整系数
export interface DeviceAdjustment {
  fontScale: number;       // 字体缩放系数
  marginScale: number;     // 边距缩放系数
  sideMargin: number;      // 左右边距(px)
}

// 视频方向调整配置
export interface OrientationAdjustment {
  fontScale: number;       // 字体缩放系数
  marginScale: number;     // 边距缩放系数
}

// 语言特定调整
export interface LanguageAdjustment {
  marginExtra: number;     // 额外边距调整(px)
  verticalExtra: number;   // 竖屏额外调整(px)
  fontScale?: number;      // 可选的字体缩放调整
}

// 完整的字幕字体配置
export interface SubtitleFontConfig {
  // 用户选择的字体大小级别
  userFontLevel: FontSizeLevel;
  
  // 字体大小缩放映射
  fontSizeScales: FontSizeScale;
  
  // 分辨率配置映射
  resolutionConfigs: Record<ResolutionLevel, ResolutionFontConfig>;
  
  // 设备类型调整映射
  deviceAdjustments: Record<DeviceType, DeviceAdjustment>;
  
  // 视频方向调整映射
  orientationAdjustments: Record<VideoOrientation, OrientationAdjustment>;
  
  // 语言调整映射
  languageAdjustments: Record<string, LanguageAdjustment>;
  
  // 是否启用自适应调整
  enableAdaptive: boolean;
  
  // 最后更新时间
  lastUpdated: number;
}

// 默认字体大小缩放配置
export const DEFAULT_FONT_SIZE_SCALES: FontSizeScale = {
  [FontSizeLevel.EXTRA_SMALL]: 0.7,
  [FontSizeLevel.SMALL]: 0.85,
  [FontSizeLevel.MEDIUM]: 1.0,
  [FontSizeLevel.LARGE]: 1.15,
  [FontSizeLevel.EXTRA_LARGE]: 1.3
};

// 默认分辨率配置
export const DEFAULT_RESOLUTION_CONFIGS: Record<ResolutionLevel, ResolutionFontConfig> = {
  [ResolutionLevel.SD_480P]: {
    baseSize: 18,
    minSize: 12,
    maxSize: 24,
    marginBase: 20
  },
  [ResolutionLevel.HD_720P]: {
    baseSize: 24,
    minSize: 16,
    maxSize: 32,
    marginBase: 30
  },
  [ResolutionLevel.FHD_1080P]: {
    baseSize: 32,
    minSize: 22,
    maxSize: 42,
    marginBase: 40
  },
  [ResolutionLevel.QHD_1440P]: {
    baseSize: 44,
    minSize: 30,
    maxSize: 58,
    marginBase: 50
  },
  [ResolutionLevel.UHD_4K]: {
    baseSize: 64,
    minSize: 44,
    maxSize: 84,
    marginBase: 60
  },
  [ResolutionLevel.UHD_8K]: {
    baseSize: 120,
    minSize: 84,
    maxSize: 156,
    marginBase: 80
  }
};

// 默认设备调整配置
export const DEFAULT_DEVICE_ADJUSTMENTS: Record<DeviceType, DeviceAdjustment> = {
  [DeviceType.MOBILE_PORTRAIT]: {
    fontScale: 0.65,
    marginScale: 1.2,
    sideMargin: 16
  },
  [DeviceType.MOBILE_LANDSCAPE]: {
    fontScale: 0.8,
    marginScale: 1.0,
    sideMargin: 20
  },
  [DeviceType.TABLET]: {
    fontScale: 0.9,
    marginScale: 1.0,
    sideMargin: 24
  },
  [DeviceType.DESKTOP]: {
    fontScale: 1.0,
    marginScale: 1.0,
    sideMargin: 32
  }
};

// 默认视频方向调整配置
export const DEFAULT_ORIENTATION_ADJUSTMENTS: Record<VideoOrientation, OrientationAdjustment> = {
  [VideoOrientation.HORIZONTAL]: {
    fontScale: 1.0,
    marginScale: 1.0
  },
  [VideoOrientation.VERTICAL]: {
    fontScale: 0.75,
    marginScale: 1.3
  },
  [VideoOrientation.SQUARE]: {
    fontScale: 0.9,
    marginScale: 1.1
  }
};

// 默认语言调整配置
export const DEFAULT_LANGUAGE_ADJUSTMENTS: Record<string, LanguageAdjustment> = {
  'zh': {
    marginExtra: 8,
    verticalExtra: 12,
    fontScale: 1.0
  },
  'zh-cn': {
    marginExtra: 8,
    verticalExtra: 12,
    fontScale: 1.0
  },
  'zh-tw': {
    marginExtra: 8,
    verticalExtra: 12,
    fontScale: 1.0
  },
  'ja': {
    marginExtra: 6,
    verticalExtra: 10,
    fontScale: 0.95
  },
  'ko': {
    marginExtra: 4,
    verticalExtra: 8,
    fontScale: 0.95
  },
  'en': {
    marginExtra: 4,
    verticalExtra: 6,
    fontScale: 1.0
  },
  'default': {
    marginExtra: 4,
    verticalExtra: 6,
    fontScale: 1.0
  }
};

// 默认完整配置
export const DEFAULT_SUBTITLE_FONT_CONFIG: SubtitleFontConfig = {
  userFontLevel: FontSizeLevel.MEDIUM,
  fontSizeScales: DEFAULT_FONT_SIZE_SCALES,
  resolutionConfigs: DEFAULT_RESOLUTION_CONFIGS,
  deviceAdjustments: DEFAULT_DEVICE_ADJUSTMENTS,
  orientationAdjustments: DEFAULT_ORIENTATION_ADJUSTMENTS,
  languageAdjustments: DEFAULT_LANGUAGE_ADJUSTMENTS,
  enableAdaptive: true,
  lastUpdated: Date.now()
};

// 字体大小级别显示名称
export const FONT_SIZE_LEVEL_NAMES: Record<FontSizeLevel, string> = {
  [FontSizeLevel.EXTRA_SMALL]: '极小',
  [FontSizeLevel.SMALL]: '小',
  [FontSizeLevel.MEDIUM]: '标准',
  [FontSizeLevel.LARGE]: '大',
  [FontSizeLevel.EXTRA_LARGE]: '极大'
};

// 字体大小级别描述
export const FONT_SIZE_LEVEL_DESCRIPTIONS: Record<FontSizeLevel, string> = {
  [FontSizeLevel.EXTRA_SMALL]: '适合大屏幕或不喜欢字幕遮挡的用户',
  [FontSizeLevel.SMALL]: '适合正常视力用户的较小字体',
  [FontSizeLevel.MEDIUM]: '推荐的标准字体大小，适合大多数用户',
  [FontSizeLevel.LARGE]: '适合需要更清晰字体的用户',
  [FontSizeLevel.EXTRA_LARGE]: '适合视力较弱或小屏幕设备的用户'
};
